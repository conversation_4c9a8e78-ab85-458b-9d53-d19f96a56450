import { useEffect } from 'react';
import * as WebBrowser from 'expo-web-browser';
import { useAuthRequest, useAutoDiscovery, makeRedirectUri } from 'expo-auth-session';
import { View, Text, TouchableOpacity, Alert, StyleSheet, ScrollView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';


WebBrowser.maybeCompleteAuthSession();


// Configure redirect URI for different platforms
const redirectTo = Platform.OS === 'web'
  ? 'http://localhost:8082/callback'  // Explicit web redirect URI
  : makeRedirectUri({
      scheme: 'learningcoachcommunity',
      native: 'learningcoachcommunity://okta-callback',
    });

export default function App() {
    const oktaDomain = process.env.EXPO_PUBLIC_OKTA_ISSUER || 'https://integrator-5743111.okta.com/oauth2/default';
    const clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || 'your-client-id-here';

    console.log('Okta Domain: ', oktaDomain, 'Client ID: ', clientId);
    console.log('Redirect URI: ', redirectTo);
  console.log('Platform: ', Platform.OS);

  // Endpoint
  const discovery = useAutoDiscovery(oktaDomain);
  // Request
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId: clientId,
      scopes: ['openid', 'profile'],
      redirectUri: redirectTo,
    },
    discovery
  );

  useEffect(() => {
    if (response?.type === 'success') {
      const { code } = response.params;
      Alert.alert('Success!', `Received authorization code: ${code?.substring(0, 20)}...`);
      console.log('✅ Okta auth success:', { code });
    } else if (response?.type === 'error') {
      Alert.alert('Error', response.error?.message || 'Authentication failed');
      console.error('❌ Okta auth error:', response.error);
    }
  }, [response]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>🔐 Okta Authentication</Text>
          <Text style={styles.subtitle}>Android Testing</Text>
        </View>

        <View style={styles.configCard}>
          <Text style={styles.cardTitle}>Configuration</Text>
          <Text style={styles.configText}>Domain: {oktaDomain}</Text>
          <Text style={styles.configText}>Client ID: {clientId?.substring(0, 12)}...</Text>
          <Text style={styles.configText}>Redirect: {redirectTo}</Text>
          <Text style={styles.configText}>
            Discovery: {discovery ? '✅ Ready' : '⏳ Loading...'}
          </Text>
          <Text style={styles.configText}>
            Request: {request ? '✅ Ready' : '⏳ Preparing...'}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.loginButton, !request && styles.loginButtonDisabled]}
          disabled={!request}
          onPress={() => promptAsync()}
        >
          <Text style={styles.loginButtonText}>
            {!request ? '⏳ Preparing...' : '🚀 Login with Okta'}
          </Text>
        </TouchableOpacity>

        {response && (
          <View style={styles.responseCard}>
            <Text style={styles.responseTitle}>Response:</Text>
            <Text style={styles.responseText}>
              Type: {response.type}
            </Text>
            {response.type === 'success' && (
              <Text style={styles.responseText}>
                Code: {response.params.code?.substring(0, 20)}...
              </Text>
            )}
            {response.type === 'error' && (
              <Text style={styles.errorText}>
                Error: {response.error?.message}
              </Text>
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1E293B',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
  },
  configCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 12,
  },
  configText: {
    fontSize: 14,
    color: '#475569',
    marginBottom: 6,
    fontFamily: 'monospace',
  },
  loginButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonDisabled: {
    backgroundColor: '#94A3B8',
    shadowOpacity: 0,
    elevation: 0,
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  responseCard: {
    backgroundColor: '#F1F5F9',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#CBD5E1',
  },
  responseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8,
  },
  responseText: {
    fontSize: 14,
    color: '#475569',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
});
