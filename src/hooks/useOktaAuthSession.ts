/**
 * Okta Authentication Hook following Expo's official documentation pattern
 * 
 * This hook implements the exact pattern shown in Expo's documentation for Okta integration.
 * It uses useAuthRequest and useAutoDiscovery as recommended.
 */

import { useEffect, useState } from 'react';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, useAuthRequest, useAutoDiscovery } from 'expo-auth-session';
import { AuthenticationData } from '@/types/auth';

// Complete auth session for web
WebBrowser.maybeCompleteAuthSession();

interface UseOktaAuthSessionConfig {
  issuer?: string;
  clientId?: string;
  scopes?: string[];
  redirectUri?: string;
}

interface UseOktaAuthSessionReturn {
  signIn: () => void;
  authData: AuthenticationData | null;
  isLoading: boolean;
  error: string | null;
  request: any;
  response: any;
  promptAsync: () => Promise<void>;
  exchangeCodeAsync: (params: any, discovery?: any) => Promise<any>;
  refreshAsync: (params: any, discovery?: any) => Promise<any>;
  revokeAsync: (params: any, discovery?: any) => Promise<any>;
}

/**
 * Hook for Okta authentication following Expo's official pattern exactly
 */
export function useOktaAuthSession(config?: UseOktaAuthSessionConfig): UseOktaAuthSessionReturn {
  const [authData, setAuthData] = useState<AuthenticationData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Configuration
  const issuer = config?.issuer || process.env.EXPO_PUBLIC_OKTA_ISSUER!;
  const clientId = config?.clientId || process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;
  const scopes = config?.scopes || ['openid', 'profile'];
  
  // Use Okta's required redirect URI pattern as per Expo docs
  // Using direct string to match what works in the class-based approach
  const redirectUri = config?.redirectUri || 'com.okta.integrator-5743111:/callback';

  console.log('🔐 [useOktaAuthSession] Configuration:', {
    issuer,
    clientId,
    redirectUri,
    scopes,
  });

  // Use auto discovery as recommended by Expo docs
  const discovery = useAutoDiscovery(issuer);

  // Use the official useAuthRequest hook
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId,
      scopes,
      redirectUri,
    },
    discovery
  );

  // Handle authentication response
  useEffect(() => {
    if (response?.type === 'success') {
      console.log('🔐 [useOktaAuthSession] Auth success:', response);
      const { code } = response.params;
      
      // TODO: Exchange code for tokens
      // For now, create mock auth data
      const mockAuthData: AuthenticationData = {
        accessToken: 'mock-access-token',
        idToken: 'mock-id-token',
        refreshToken: 'mock-refresh-token',
        expiresAt: Date.now() + 3600000, // 1 hour
        tokenType: 'Bearer',
        scopes: scopes,
      };
      
      setAuthData(mockAuthData);
      setIsLoading(false);
      setError(null);
    } else if (response?.type === 'error') {
      console.error('🔐 [useOktaAuthSession] Auth error:', response.error);
      setError(response.error?.message || 'Authentication failed');
      setIsLoading(false);
    } else if (response?.type === 'cancel') {
      console.log('🔐 [useOktaAuthSession] Auth cancelled');
      setError('Authentication was cancelled');
      setIsLoading(false);
    }
  }, [response, scopes]);

  const signIn = () => {
    if (!request) {
      setError('Authentication request not ready');
      return;
    }

    console.log('🔐 [useOktaAuthSession] Starting authentication...');
    setIsLoading(true);
    setError(null);
    promptAsync();
  };

  // Mock implementations for compatibility with AuthContext
  const mockExchangeCodeAsync = async (params: any, discovery?: any) => {
    console.log('🔐 [useOktaAuthSession] Mock exchangeCodeAsync called');
    return { accessToken: 'mock-token', idToken: 'mock-id-token' };
  };

  const mockRefreshAsync = async (params: any, discovery?: any) => {
    console.log('🔐 [useOktaAuthSession] Mock refreshAsync called');
    return { accessToken: 'mock-refreshed-token' };
  };

  const mockRevokeAsync = async (params: any, discovery?: any) => {
    console.log('🔐 [useOktaAuthSession] Mock revokeAsync called');
    return {};
  };

  const mockPromptAsync = async () => {
    if (promptAsync) {
      return promptAsync();
    }
    throw new Error('promptAsync not available');
  };

  return {
    signIn,
    authData,
    isLoading,
    error,
    request,
    response,
    promptAsync: mockPromptAsync,
    exchangeCodeAsync: mockExchangeCodeAsync,
    refreshAsync: mockRefreshAsync,
    revokeAsync: mockRevokeAsync,
  };
}
